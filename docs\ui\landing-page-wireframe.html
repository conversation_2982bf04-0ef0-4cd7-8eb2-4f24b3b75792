<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>E-Senpai - Gaming Companion Platform</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Courier New", monospace;
        background-color: #ffffff;
        color: #000000;
        line-height: 1.6;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
      }

      /* Header */
      .header {
        border-bottom: 2px solid #000000;
        padding: 20px 0;
        background-color: #ffffff;
        position: sticky;
        top: 0;
        z-index: 100;
      }

      .nav {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .logo {
        font-size: 24px;
        font-weight: bold;
        border: 2px solid #000000;
        padding: 8px 16px;
        background-color: #000000;
        color: #ffffff;
      }

      .nav-links {
        display: flex;
        list-style: none;
        gap: 0;
      }

      .nav-links li {
        border: 2px solid #000000;
        border-left: none;
      }

      .nav-links li:first-child {
        border-left: 2px solid #000000;
      }

      .nav-links a {
        display: block;
        padding: 12px 20px;
        text-decoration: none;
        color: #000000;
        background-color: #ffffff;
        transition: all 0.2s;
      }

      .nav-links a:hover {
        background-color: #000000;
        color: #ffffff;
      }

      .cta-button {
        border: 2px solid #000000;
        padding: 12px 24px;
        background-color: #000000;
        color: #ffffff;
        text-decoration: none;
        font-weight: bold;
        transition: all 0.2s;
      }

      .cta-button:hover {
        background-color: #ffffff;
        color: #000000;
      }

      /* Hero Section */
      .hero {
        border-bottom: 2px solid #000000;
        padding: 80px 0;
        text-align: center;
      }

      .hero h1 {
        font-size: 48px;
        margin-bottom: 20px;
        border: 2px solid #000000;
        padding: 20px;
        display: inline-block;
        background-color: #000000;
        color: #ffffff;
      }

      .hero p {
        font-size: 20px;
        margin-bottom: 40px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
      }

      .hero-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        flex-wrap: wrap;
      }

      .button {
        border: 2px solid #000000;
        padding: 16px 32px;
        text-decoration: none;
        font-weight: bold;
        font-size: 16px;
        transition: all 0.2s;
        display: inline-block;
      }

      .button-primary {
        background-color: #000000;
        color: #ffffff;
      }

      .button-primary:hover {
        background-color: #ffffff;
        color: #000000;
      }

      .button-secondary {
        background-color: #ffffff;
        color: #000000;
      }

      .button-secondary:hover {
        background-color: #000000;
        color: #ffffff;
      }

      /* Features Section */
      .features {
        border-bottom: 2px solid #000000;
        padding: 80px 0;
      }

      .section-title {
        text-align: center;
        font-size: 36px;
        margin-bottom: 60px;
        border: 2px solid #000000;
        padding: 20px;
        display: inline-block;
        background-color: #ffffff;
        color: #000000;
        width: 100%;
      }

      .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 0;
      }

      .feature-card {
        border: 2px solid #000000;
        padding: 40px 30px;
        text-align: center;
        background-color: #ffffff;
        border-left: none;
      }

      .feature-card:first-child {
        border-left: 2px solid #000000;
      }

      .feature-icon {
        font-size: 48px;
        margin-bottom: 20px;
        border: 2px solid #000000;
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        background-color: #000000;
        color: #ffffff;
      }

      .feature-card h3 {
        font-size: 24px;
        margin-bottom: 15px;
      }

      .feature-card p {
        font-size: 16px;
      }

      /* How It Works */
      .how-it-works {
        border-bottom: 2px solid #000000;
        padding: 80px 0;
        background-color: #ffffff;
      }

      .steps {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 0;
        margin-top: 40px;
      }

      .step {
        border: 2px solid #000000;
        padding: 30px 20px;
        text-align: center;
        border-left: none;
      }

      .step:first-child {
        border-left: 2px solid #000000;
      }

      .step-number {
        font-size: 32px;
        font-weight: bold;
        border: 2px solid #000000;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        background-color: #000000;
        color: #ffffff;
      }

      /* Footer */
      .footer {
        padding: 40px 0;
        background-color: #000000;
        color: #ffffff;
      }

      .footer-content {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 40px;
      }

      .footer-section h4 {
        margin-bottom: 20px;
        font-size: 18px;
      }

      .footer-section ul {
        list-style: none;
      }

      .footer-section a {
        color: #ffffff;
        text-decoration: none;
        line-height: 2;
      }

      .footer-section a:hover {
        text-decoration: underline;
      }

      .footer-bottom {
        border-top: 2px solid #ffffff;
        margin-top: 40px;
        padding-top: 20px;
        text-align: center;
      }

      /* Mobile Responsive */
      @media (max-width: 768px) {
        .nav {
          flex-direction: column;
          gap: 20px;
        }

        .nav-links {
          flex-direction: column;
          width: 100%;
        }

        .nav-links li {
          border-left: 2px solid #000000;
          border-top: none;
        }

        .nav-links li:first-child {
          border-top: 2px solid #000000;
        }

        .hero h1 {
          font-size: 32px;
        }

        .hero-buttons {
          flex-direction: column;
          align-items: center;
        }

        .features-grid {
          grid-template-columns: 1fr;
        }

        .feature-card {
          border-left: 2px solid #000000;
          border-top: none;
        }

        .feature-card:first-child {
          border-top: 2px solid #000000;
        }

        .steps {
          grid-template-columns: 1fr;
        }

        .step {
          border-left: 2px solid #000000;
          border-top: none;
        }

        .step:first-child {
          border-top: 2px solid #000000;
        }
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <header class="header">
      <div class="container">
        <nav class="nav">
          <div class="logo">E-Senpai</div>
          <ul class="nav-links">
            <li><a href="#features">Features</a></li>
            <li><a href="#how-it-works">How It Works</a></li>
            <li><a href="#pricing">Pricing</a></li>
            <li><a href="#about">About</a></li>
          </ul>
          <a href="#signup" class="cta-button">Get Started</a>
        </nav>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
      <div class="container">
        <h1>Find Your Gaming Companion</h1>
        <p>
          Connect with skilled players, learn new strategies, and level up your
          gaming experience with E-Senpai's companion platform.
        </p>
        <div class="hero-buttons">
          <a href="#signup" class="button button-primary">Start Playing</a>
          <a href="#demo" class="button button-secondary">Watch Demo</a>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
      <div class="container">
        <h2 class="section-title">Core Features</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🎮</div>
            <h3>Game Matching</h3>
            <p>
              Find companions for your favorite games with our intelligent
              matching system based on skill level and playstyle.
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">⭐</div>
            <h3>Skill-Based Pairing</h3>
            <p>
              Connect with players at your skill level or find mentors to help
              you improve your gameplay.
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">💬</div>
            <h3>Real-Time Chat</h3>
            <p>
              Communicate with your gaming companions through our integrated
              chat system during sessions.
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🏆</div>
            <h3>Achievement Tracking</h3>
            <p>
              Track your progress, earn achievements, and showcase your gaming
              accomplishments.
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🔒</div>
            <h3>Safe Environment</h3>
            <p>
              Verified profiles and community moderation ensure a safe and
              positive gaming experience.
            </p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">📱</div>
            <h3>Cross-Platform</h3>
            <p>
              Access E-Senpai from any device - desktop, mobile, or tablet for
              gaming on the go.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section class="how-it-works" id="how-it-works">
      <div class="container">
        <h2 class="section-title">How It Works</h2>
        <div class="steps">
          <div class="step">
            <div class="step-number">1</div>
            <h3>Create Profile</h3>
            <p>
              Sign up and create your gaming profile with your favorite games
              and skill levels.
            </p>
          </div>
          <div class="step">
            <div class="step-number">2</div>
            <h3>Find Companions</h3>
            <p>
              Browse available gaming companions or let our system match you
              with compatible players.
            </p>
          </div>
          <div class="step">
            <div class="step-number">3</div>
            <h3>Book Session</h3>
            <p>
              Schedule gaming sessions with your chosen companions at times that
              work for both of you.
            </p>
          </div>
          <div class="step">
            <div class="step-number">4</div>
            <h3>Play Together</h3>
            <p>
              Join your gaming session, communicate through chat, and enjoy an
              enhanced gaming experience.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="features">
      <div class="container">
        <h2 class="section-title">Platform Stats</h2>
        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">👥</div>
            <h3>10,000+</h3>
            <p>Active Gaming Companions</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">🎯</div>
            <h3>50+</h3>
            <p>Supported Games</p>
          </div>
          <div class="feature-card">
            <div class="feature-icon">⏱️</div>
            <h3>100,000+</h3>
            <p>Hours Played Together</p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="hero">
      <div class="container">
        <h2
          style="
            font-size: 36px;
            border: 2px solid #000000;
            padding: 20px;
            background-color: #ffffff;
            color: #000000;
            margin-bottom: 20px;
          "
        >
          Ready to Level Up?
        </h2>
        <p>
          Join thousands of gamers who have found their perfect gaming
          companions on E-Senpai.
        </p>
        <div class="hero-buttons">
          <a href="#signup" class="button button-primary">Join E-Senpai</a>
          <a href="#contact" class="button button-secondary">Contact Us</a>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>Platform</h4>
            <ul>
              <li><a href="#features">Features</a></li>
              <li><a href="#pricing">Pricing</a></li>
              <li><a href="#games">Supported Games</a></li>
              <li><a href="#safety">Safety</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Community</h4>
            <ul>
              <li><a href="#discord">Discord</a></li>
              <li><a href="#reddit">Reddit</a></li>
              <li><a href="#twitter">Twitter</a></li>
              <li><a href="#blog">Blog</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Support</h4>
            <ul>
              <li><a href="#help">Help Center</a></li>
              <li><a href="#contact">Contact Us</a></li>
              <li><a href="#guidelines">Community Guidelines</a></li>
              <li><a href="#report">Report Issue</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>Legal</h4>
            <ul>
              <li><a href="#privacy">Privacy Policy</a></li>
              <li><a href="#terms">Terms of Service</a></li>
              <li><a href="#cookies">Cookie Policy</a></li>
              <li><a href="#dmca">DMCA</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>
            &copy; 2024 E-Senpai. All rights reserved. | Gaming Companion
            Platform
          </p>
        </div>
      </div>
    </footer>
  </body>
</html>
