{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_42788bc2._.js", "server/edge/chunks/41428_@formatjs_intl-localematcher_lib_d637b060._.js", "server/edge/chunks/4294a_@supabase_auth-js_dist_module_51b8369e._.js", "server/edge/chunks/node_modules__pnpm_0c0ce5b1._.js", "server/edge/chunks/[root-of-the-server]__569f5a63._.js", "server/edge/chunks/packages_webapp_edge-wrapper_1a52fb86.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|ingest|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|ingest|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oKH2XG+pncrIx4/k4x1NQVrid0DXNmC2fOUTyxzd+Sk=", "__NEXT_PREVIEW_MODE_ID": "f75d4904cce4e34ab8c3d1db85e5a235", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4aa46cb3780e0399164881265932e1b422beb396f4844cfa8ee53e60dcb45771", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d1ff8a103b3244ed4dff699f8e1c6933538f7a646c392f4cd74581d60c1064e9"}}}, "instrumentation": null, "functions": {}}